graph TB
subgraph "Interface Layer"
A[REST API]
B[GraphQL API]
C[gRPC Service]
D[Event Handlers]
end

    subgraph "Application Layer"
        E[Use Cases]
        F[Command Handlers]
        G[Query Handlers]
        H[Application Services]
    end

    subgraph "Domain Layer"
        I[Entities]
        J[Value Objects]
        K[Domain Services]
        L[Repository Interfaces]
    end

    subgraph "Infrastructure Layer"
        M[Database]
        N[Message Brokers]
        O[External Services]
        P[Security]
    end

    A --> E
    B --> F
    C --> G
    D --> H
    E --> I
    F --> J
    G --> K
    H --> L
    L --> M
    L --> N
    L --> O
    L --> P
