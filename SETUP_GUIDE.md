# 🚀 Docker Setup Guide - Tóm Tắt Nhanh

## ⚡ Quick Commands

### Sử Dụng Project Hiện Tại
```bash
# Development
./scripts/docker-commands.sh dev

# Production  
./scripts/docker-commands.sh prod

# Status check
./scripts/docker-commands.sh status
./scripts/docker-commands.sh health
```

### URLs
- **Dev App**: http://localhost:8000/health
- **Prod App**: http://localhost:8001/health  
- **Nginx**: http://localhost/health
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090

---

## 🛠️ Setup Project Mới Từ Đầu

### 1. T<PERSON>o <PERSON>u Trúc
```bash
mkdir my-project && cd my-project
mkdir -p src tests scripts monitoring/{grafana/{dashboards,datasources},prometheus}
```

### 2. Copy Files Từ Project Này
```bash
# Copy toàn bộ Docker configuration
cp ../practice/Dockerfile* .
cp ../practice/docker-compose*.yml .
cp ../practice/.dockerignore .
cp ../practice/nginx.conf .
cp -r ../practice/scripts .
cp -r ../practice/monitoring .
```

### 3. Customize cho Tech Stack

#### Node.js
```bash
# Thay Dockerfile
FROM node:alpine
COPY package*.json ./
RUN npm ci --only=production
COPY . .
CMD ["npm", "start"]
```

#### Python
```bash
# Thay Dockerfile  
FROM python:alpine
COPY requirements.txt ./
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "src/app.py"]
```

#### Go
```bash
# Thay Dockerfile
FROM golang:alpine AS builder
COPY go.* ./
RUN go mod download
COPY . .
RUN go build -o app

FROM alpine:latest
COPY --from=builder /app/app .
CMD ["./app"]
```

### 4. Update Configuration
```bash
# Update service names trong docker-compose.yml
# Update health check endpoints
# Update environment variables
# Update port mappings nếu cần
```

### 5. Test Setup
```bash
chmod +x scripts/docker-commands.sh
./scripts/docker-commands.sh dev-build
./scripts/docker-commands.sh dev
./scripts/docker-commands.sh health
```

---

## 📋 Checklist Setup Hoàn Chỉnh

### ✅ Files Cần Thiết
- [ ] `Dockerfile` (production)
- [ ] `Dockerfile.dev` (development)  
- [ ] `docker-compose.yml` (main config)
- [ ] `docker-compose.override.yml` (dev overrides)
- [ ] `nginx.conf` (load balancer)
- [ ] `.dockerignore` (build optimization)
- [ ] `scripts/docker-commands.sh` (management)
- [ ] `scripts/load-test.js` (performance test)
- [ ] `monitoring/prometheus.yml` (metrics)
- [ ] `monitoring/grafana/` (dashboards)

### ✅ Features Included
- [ ] Development environment với hot reload
- [ ] Production environment optimized
- [ ] Load balancer với Nginx
- [ ] Database services (PostgreSQL + Redis)
- [ ] Monitoring stack (Prometheus + Grafana)
- [ ] Health checks cho tất cả services
- [ ] Performance testing với k6
- [ ] Security best practices
- [ ] Resource limits và management
- [ ] Easy management scripts

### ✅ Testing Checklist
- [ ] `./scripts/docker-commands.sh dev` works
- [ ] `./scripts/docker-commands.sh prod` works
- [ ] Health endpoints respond correctly
- [ ] Monitoring dashboards accessible
- [ ] Database connections working
- [ ] Load balancer routing correctly
- [ ] Performance tests pass
- [ ] All containers have proper resource limits

---

## 🎯 Production Deployment Tips

### Environment Variables
```bash
# Tạo .env file cho production
POSTGRES_PASSWORD=secure_password
REDIS_PASSWORD=secure_redis_password
JWT_SECRET=your_jwt_secret
API_KEY=your_api_key
```

### SSL/HTTPS Setup
```bash
# Thêm SSL certificates vào nginx.conf
server {
    listen 443 ssl;
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
}
```

### Scaling
```bash
# Scale production app
docker-compose up -d --scale app-prod=3

# Update nginx upstream
upstream deno_backend {
    server app-prod-1:8000;
    server app-prod-2:8000;
    server app-prod-3:8000;
}
```

### Backup Strategy
```bash
# Database backup
docker exec postgres pg_dump -U user dbname > backup.sql

# Volume backup  
docker run --rm -v postgres-data:/data -v $(pwd):/backup alpine tar czf /backup/postgres-backup.tar.gz /data
```

---

## 🔧 Troubleshooting

### Common Issues
```bash
# Port conflicts
./scripts/docker-commands.sh clean
docker system prune -f

# Permission issues
sudo chown -R $USER:$USER .
chmod +x scripts/docker-commands.sh

# Build cache issues
docker-compose build --no-cache

# Network issues
docker network prune
docker-compose down && docker-compose up -d
```

### Debug Commands
```bash
# Check logs
./scripts/docker-commands.sh logs

# Shell access
./scripts/docker-commands.sh shell

# Container inspection
docker inspect container_name

# Network inspection
docker network ls
docker network inspect practice_deno-network
```

---

## 🎉 Success Criteria

Khi setup thành công, bạn sẽ có:

✅ **Development environment** với hot reload  
✅ **Production environment** optimized và secure  
✅ **Load balancer** với rate limiting  
✅ **Database persistence** cho PostgreSQL và Redis  
✅ **Monitoring stack** với Grafana dashboards  
✅ **Health checks** cho tất cả services  
✅ **Performance testing** capabilities  
✅ **Easy management** với 17 commands  
✅ **Security best practices** implemented  
✅ **Scalable architecture** ready for production  

**Congratulations! Bạn đã có một Docker setup đỉnh cao! 🚀**
